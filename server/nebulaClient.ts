// Nebula API client for server-side integration
// Uses direct API calls with wallet and chain context
// Based on: https://portal.thirdweb.com/nebula/api-reference/chat

const NEBULA_API_BASE = "https://nebula-api.thirdweb.com";

export interface NebulaMessage {
  message: string;
  stream?: boolean;
  session_id?: string;
}

export interface NebulaResponse {
  message: string;
  actions?: Array<{
    session_id: string;
    request_id: string;
    type: string;
    source: string;
    data: string;
  }>;
  session_id: string;
  request_id: string;
  transactions?: any[];
}

export interface NebulaContextFilter {
  walletAddresses?: string[];
  chainIds?: number[];
  contractAddresses?: string[];
}

/**
 * Transform Nebula responses to represent Web3AI platform
 * Replaces references to "Nebula" with "Web3AI" to rebrand the responses
 */
function transformResponseForWeb3AI(message: string): string {
  if (!message) return message;

  // Replace various forms of "Nebula" with "Web3AI"
  let transformedMessage = message
    // Direct replacements
    .replace(/\bNebula\b/g, "Web3AI")
    .replace(/\bnebula\b/g, "Web3AI")
    .replace(/\bNEBULA\b/g, "WEB3AI")

    // Handle possessive forms
    .replace(/\bNebula's\b/g, "Web3AI's")
    .replace(/\bnebula's\b/g, "Web3AI's")

    // Handle specific thirdweb references if they appear
    .replace(/\bthirdweb's Nebula\b/g, "Web3AI")
    .replace(/\bthirdweb Nebula\b/g, "Web3AI")

    // Handle "I am Nebula" type statements
    .replace(/\bI am Nebula\b/g, "I am Web3AI")
    .replace(/\bI'm Nebula\b/g, "I'm Web3AI")

    // Handle platform identification
    .replace(/\bNebula platform\b/g, "Web3AI platform")
    .replace(/\bNebula AI\b/g, "Web3AI")
    .replace(/\bNebula assistant\b/g, "Web3AI assistant");

  return transformedMessage;
}

/**
 * Send a message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessage(
  message: string,
  sessionId?: string,
  stream: boolean = false,
  contextFilter?: NebulaContextFilter
): Promise<NebulaResponse> {
  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log("Nebula API Response:", JSON.stringify(data, null, 2));

    // Transform the response to replace "Nebula" with "Web3AI"
    if (data.message) {
      data.message = transformResponseForWeb3AI(data.message);
    }

    return data;
  } catch (error) {
    console.error("Error calling Nebula API:", error);
    throw error;
  }
}

/**
 * Send a streaming message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessageStream(
  message: string,
  sessionId?: string,
  contextFilter?: NebulaContextFilter,
  onChunk?: (chunk: string, isComplete: boolean, metadata?: any) => void
): Promise<void> {
  console.log("sendNebulaMessageStream called with:", {
    message,
    sessionId,
    contextFilter,
  });

  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;
  console.log(
    "THIRDWEB_SECRET_KEY loaded:",
    secretKey ? "✓ Present" : "✗ Missing"
  );

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream: true,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    console.log(
      "Calling Nebula API with payload:",
      JSON.stringify(payload, null, 2)
    );
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      let errorText = "Unknown error";
      try {
        errorText = await response.text();
      } catch (e) {
        console.warn("Could not read error response as text:", e);
      }
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    // Check if the response is streaming (SSE format)
    const contentType = response.headers.get("content-type");
    console.log("Nebula API response content-type:", contentType);

    if (
      contentType?.includes("text/event-stream") ||
      contentType?.includes("text/plain")
    ) {
      // Handle Server-Sent Events from Nebula API
      if (!response.body) {
        throw new Error("No response body for streaming");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let finalMetadata: any = {};
      let fullMessage = "";

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Send completion signal with metadata
            console.log("Sending completion metadata:", finalMetadata);
            onChunk?.("", true, finalMetadata);
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep incomplete line in buffer

          for (const line of lines) {
            console.log("SSE line:", line);

            if (line.startsWith("data: ")) {
              try {
                const jsonData = line.slice(6);
                if (jsonData.trim() === "[DONE]") {
                  // OpenAI-style completion marker
                  continue;
                }

                const data = JSON.parse(jsonData);
                console.log("Parsed SSE data:", data);

                // Handle different types of streaming data from Nebula API
                if (data.v !== undefined) {
                  // Nebula API delta format: {"v": "text chunk"}
                  const transformedChunk = transformResponseForWeb3AI(data.v);
                  fullMessage += transformedChunk;
                  // Send the individual chunk, not the accumulated message
                  onChunk?.(transformedChunk, false);
                } else if (data.type === "init") {
                  // Store initial metadata
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                } else if (data.choices && data.choices[0]?.delta?.content) {
                  // OpenAI-style streaming (fallback)
                  const content = transformResponseForWeb3AI(
                    data.choices[0].delta.content
                  );
                  fullMessage += content;
                  onChunk?.(fullMessage, false);
                } else if (data.message) {
                  // Direct message content (fallback)
                  const transformedMessage = transformResponseForWeb3AI(
                    data.message
                  );
                  fullMessage = transformedMessage;
                  onChunk?.(transformedMessage, false);

                  // Store metadata
                  if (data.session_id)
                    finalMetadata.session_id = data.session_id;
                  if (data.request_id)
                    finalMetadata.request_id = data.request_id;
                  if (data.actions) {
                    console.log("Nebula API returned actions:", data.actions);
                    finalMetadata.actions = data.actions;
                  }
                  if (data.transactions) {
                    console.log(
                      "Nebula API returned transactions:",
                      data.transactions
                    );
                    finalMetadata.transactions = data.transactions;
                  }
                } else if (data.content) {
                  // Content field (fallback)
                  const transformedContent = transformResponseForWeb3AI(
                    data.content
                  );
                  fullMessage += transformedContent;
                  onChunk?.(fullMessage, false);
                }
              } catch (parseError) {
                console.warn("Failed to parse SSE data:", line, parseError);
              }
            } else if (line.startsWith("event: ")) {
              console.log("SSE event:", line);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      // Handle regular JSON response (fallback)
      const data = await response.json();
      console.log("Nebula API JSON Response:", JSON.stringify(data, null, 2));

      // Transform the message before streaming
      if (data.message) {
        data.message = transformResponseForWeb3AI(data.message);
      }

      // Simulate streaming by breaking the message into chunks
      if (data.message) {
        const words = data.message.split(" ");
        for (let i = 0; i < words.length; i++) {
          const chunk = words.slice(0, i + 1).join(" ");
          onChunk?.(chunk, false);
          // Small delay to simulate streaming (twice as fast as requested)
          await new Promise((resolve) => setTimeout(resolve, 25));
        }
      }

      // Send completion with metadata
      const completionMetadata = {
        session_id: data.session_id,
        request_id: data.request_id,
        actions: data.actions,
        transactions: data.transactions,
      };
      console.log("Sending JSON completion metadata:", completionMetadata);
      onChunk?.("", true, completionMetadata);
    }
  } catch (error) {
    console.error("Error calling Nebula streaming API:", error);
    throw error;
  }
}

/**
 * Generate a new session ID for Nebula chat
 */
export function generateSessionId(): string {
  // Generate a proper UUID v4 format
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
