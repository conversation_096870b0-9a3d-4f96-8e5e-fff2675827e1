async function u(d,l,f,r,c,a){const y={message:d,stream:!0,session_id:f,contextFilter:l};try{const e=await fetch("/api/nebula/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)});if(!e.ok){const s=await e.text();throw new Error(`Nebula API error: ${e.status} - ${s}`)}if(e.body){const s=e.body.getReader(),p=new TextDecoder;let i="";try{for(;;){const{done:w,value:b}=await s.read();if(w)break;i+=p.decode(b,{stream:!0});const o=i.split(`
`);i=o.pop()||"";for(const n of o)if(n.startsWith("data: "))try{const t=JSON.parse(n.slice(6));t.type==="chunk"&&t.content?r==null||r(t.content):t.type==="complete"?c==null||c(t):t.type==="error"&&(a==null||a(t.error))}catch{console.warn("Failed to parse streaming data:",n)}}}finally{s.releaseLock()}}}catch(e){throw console.error("Error calling Nebula streaming API:",e),a==null||a(e instanceof Error?e.message:"Unknown error"),e}}export{u as sendNebulaMessageStream};
