import{c9 as c,cf as i}from"./index-BLCiG1f1.js";const L=s=>s,d=s=>s;class B extends c{constructor({body:e,cause:r,details:a,headers:n,status:u,url:l}){super("HTTP request failed.",{cause:r,details:a,metaMessages:[u&&`Status: ${u}`,`URL: ${d(l)}`,e&&`Request body: ${i(e)}`].filter(Boolean),name:"HttpRequestError"}),Object.defineProperty(this,"body",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"headers",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"url",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.body=e,this.headers=n,this.status=u,this.url=l}}class J extends c{constructor({body:e,cause:r,details:a,url:n}){super("WebSocket request failed.",{cause:r,details:a,metaMessages:[`URL: ${d(n)}`,e&&`Request body: ${i(e)}`].filter(Boolean),name:"WebSocketRequestError"})}}class W extends c{constructor({body:e,error:r,url:a}){super("RPC Request failed.",{cause:r,details:r.message,metaMessages:[`URL: ${d(a)}`,`Request body: ${i(e)}`],name:"RpcRequestError"}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.code=r.code,this.data=r.data}}class H extends c{constructor({url:e}={}){super("The socket has been closed.",{metaMessages:[e&&`URL: ${d(e)}`].filter(Boolean),name:"SocketClosedError"})}}class F extends c{constructor({body:e,url:r}){super("The request took too long to respond.",{details:"The request timed out.",metaMessages:[`URL: ${d(r)}`,`Request body: ${i(e)}`],name:"TimeoutError"})}}const A=-1;class t extends c{constructor(e,{code:r,docsPath:a,metaMessages:n,name:u,shortMessage:l}){super(l,{cause:e,docsPath:a,metaMessages:n||(e==null?void 0:e.metaMessages),name:u||"RpcError"}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=u||e.name,this.code=e instanceof W?e.code:r??A}}class o extends t{constructor(e,r){super(e,r),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=r.data}}class b extends t{constructor(e){super(e,{code:b.code,name:"ParseRpcError",shortMessage:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."})}}Object.defineProperty(b,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32700});class p extends t{constructor(e){super(e,{code:p.code,name:"InvalidRequestRpcError",shortMessage:"JSON is not a valid request object."})}}Object.defineProperty(p,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32600});class h extends t{constructor(e,{method:r}={}){super(e,{code:h.code,name:"MethodNotFoundRpcError",shortMessage:`The method${r?` "${r}"`:""} does not exist / is not available.`})}}Object.defineProperty(h,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32601});class m extends t{constructor(e){super(e,{code:m.code,name:"InvalidParamsRpcError",shortMessage:["Invalid parameters were provided to the RPC method.","Double check you have provided the correct parameters."].join(`
`)})}}Object.defineProperty(m,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32602});class f extends t{constructor(e){super(e,{code:f.code,name:"InternalRpcError",shortMessage:"An internal error was received."})}}Object.defineProperty(f,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32603});class g extends t{constructor(e){super(e,{code:g.code,name:"InvalidInputRpcError",shortMessage:["Missing or invalid parameters.","Double check you have provided the correct parameters."].join(`
`)})}}Object.defineProperty(g,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32e3});class v extends t{constructor(e){super(e,{code:v.code,name:"ResourceNotFoundRpcError",shortMessage:"Requested resource not found."}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"ResourceNotFoundRpcError"})}}Object.defineProperty(v,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32001});class R extends t{constructor(e){super(e,{code:R.code,name:"ResourceUnavailableRpcError",shortMessage:"Requested resource not available."})}}Object.defineProperty(R,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32002});class w extends t{constructor(e){super(e,{code:w.code,name:"TransactionRejectedRpcError",shortMessage:"Transaction creation failed."})}}Object.defineProperty(w,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32003});class P extends t{constructor(e,{method:r}={}){super(e,{code:P.code,name:"MethodNotSupportedRpcError",shortMessage:`Method${r?` "${r}"`:""} is not supported.`})}}Object.defineProperty(P,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32004});class y extends t{constructor(e){super(e,{code:y.code,name:"LimitExceededRpcError",shortMessage:"Request exceeds defined limit."})}}Object.defineProperty(y,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32005});class E extends t{constructor(e){super(e,{code:E.code,name:"JsonRpcVersionUnsupportedError",shortMessage:"Version of JSON-RPC protocol is not supported."})}}Object.defineProperty(E,"code",{enumerable:!0,configurable:!0,writable:!0,value:-32006});class j extends o{constructor(e){super(e,{code:j.code,name:"UserRejectedRequestError",shortMessage:"User rejected the request."})}}Object.defineProperty(j,"code",{enumerable:!0,configurable:!0,writable:!0,value:4001});class x extends o{constructor(e){super(e,{code:x.code,name:"UnauthorizedProviderError",shortMessage:"The requested method and/or account has not been authorized by the user."})}}Object.defineProperty(x,"code",{enumerable:!0,configurable:!0,writable:!0,value:4100});class M extends o{constructor(e,{method:r}={}){super(e,{code:M.code,name:"UnsupportedProviderMethodError",shortMessage:`The Provider does not support the requested method${r?` " ${r}"`:""}.`})}}Object.defineProperty(M,"code",{enumerable:!0,configurable:!0,writable:!0,value:4200});class O extends o{constructor(e){super(e,{code:O.code,name:"ProviderDisconnectedError",shortMessage:"The Provider is disconnected from all chains."})}}Object.defineProperty(O,"code",{enumerable:!0,configurable:!0,writable:!0,value:4900});class q extends o{constructor(e){super(e,{code:q.code,name:"ChainDisconnectedError",shortMessage:"The Provider is not connected to the requested chain."})}}Object.defineProperty(q,"code",{enumerable:!0,configurable:!0,writable:!0,value:4901});class T extends o{constructor(e){super(e,{code:T.code,name:"SwitchChainError",shortMessage:"An error occurred when attempting to switch chain."})}}Object.defineProperty(T,"code",{enumerable:!0,configurable:!0,writable:!0,value:4902});class U extends o{constructor(e){super(e,{code:U.code,name:"UnsupportedNonOptionalCapabilityError",shortMessage:"This Wallet does not support a capability that was not marked as optional."})}}Object.defineProperty(U,"code",{enumerable:!0,configurable:!0,writable:!0,value:5700});class k extends o{constructor(e){super(e,{code:k.code,name:"UnsupportedChainIdError",shortMessage:"This Wallet does not support the requested chain ID."})}}Object.defineProperty(k,"code",{enumerable:!0,configurable:!0,writable:!0,value:5710});class $ extends o{constructor(e){super(e,{code:$.code,name:"DuplicateIdError",shortMessage:"There is already a bundle submitted with this ID."})}}Object.defineProperty($,"code",{enumerable:!0,configurable:!0,writable:!0,value:5720});class S extends o{constructor(e){super(e,{code:S.code,name:"UnknownBundleIdError",shortMessage:"This bundle id is unknown / has not been submitted"})}}Object.defineProperty(S,"code",{enumerable:!0,configurable:!0,writable:!0,value:5730});class C extends o{constructor(e){super(e,{code:C.code,name:"BundleTooLargeError",shortMessage:"The call bundle is too large for the Wallet to process."})}}Object.defineProperty(C,"code",{enumerable:!0,configurable:!0,writable:!0,value:5740});class I extends o{constructor(e){super(e,{code:I.code,name:"AtomicReadyWalletRejectedUpgradeError",shortMessage:"The Wallet can support atomicity after an upgrade, but the user rejected the upgrade."})}}Object.defineProperty(I,"code",{enumerable:!0,configurable:!0,writable:!0,value:5750});class N extends o{constructor(e){super(e,{code:N.code,name:"AtomicityNotSupportedError",shortMessage:"The wallet does not support atomic execution but the request requires it."})}}Object.defineProperty(N,"code",{enumerable:!0,configurable:!0,writable:!0,value:5760});class z extends t{constructor(e){super(e,{name:"UnknownRpcError",shortMessage:"An unknown RPC error occurred."})}}export{N as A,C as B,q as C,$ as D,B as H,f as I,E as J,y as L,P as M,O as P,W as R,T as S,w as T,j as U,J as W,g as a,z as b,I as c,S as d,k as e,U as f,L as g,M as h,x as i,R as j,v as k,m as l,h as m,p as n,b as o,F as p,d as q,H as r,o as s,t};
