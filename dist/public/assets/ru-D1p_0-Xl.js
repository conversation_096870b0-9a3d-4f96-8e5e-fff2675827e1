const n=e=>({connectionScreen:{inProgress:"Ожидание подтверждения",failed:"Подключение не удалось",instruction:`Примите запрос на подключение в ${e}`,retry:"Попробовать снова"},getStartedScreen:{instruction:`Отсканируйте QR-код, чтобы скачать приложение ${e}`},scanScreen:{instruction:`Для подключения отсканируйте QR-код с помощью приложения ${e}`},getStartedLink:`Ещё нет ${e}?`,download:{chrome:"Скачать расширение для Chrome",android:"Скачать в Google Play",iOS:"Скачать в App Store"}});export{n as default};
