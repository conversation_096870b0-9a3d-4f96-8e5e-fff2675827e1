import{z as r,d0 as a,r as c}from"./index-BLCiG1f1.js";import{d}from"./detectExtension-CzSaTPm3.js";const e="0xbd85b039",o=[{type:"uint256",name:"id"}],n=[{type:"uint256"}];function s(t){return d({availableSelectors:t,method:[e,o,n]})}function u(t){return r(o,[t.id])}function l(t){return e+u(t).slice(2)}function m(t){return a(n,t)[0]}async function S(t){return c({contract:t.contract,method:[e,o,n],params:[t.id]})}export{e as FN_SELECTOR,m as decodeTotalSupplyResult,l as encodeTotalSupply,u as encodeTotalSupplyParams,s as isTotalSupplySupported,S as totalSupply};
