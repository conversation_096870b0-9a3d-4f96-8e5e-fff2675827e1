var C=Object.defineProperty;var B=(e,r,t)=>r in e?C(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t;var v=(e,r,t)=>B(e,typeof r!="symbol"?r+"":r,t);var _=async(e,r)=>{let t=typeof r=="function"?await r(e):r;if(t)return e.scheme==="bearer"?`Bearer ${t}`:e.scheme==="basic"?`Basic ${btoa(t)}`:t},j={bodySerializer:e=>JSON.stringify(e,(r,t)=>typeof t=="bigint"?t.toString():t)},O=e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},W=e=>{switch(e){case"form":return",";case"pipeDelimited":return"|";case"spaceDelimited":return"%20";default:return","}},$=e=>{switch(e){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},q=({allowReserved:e,explode:r,name:t,style:c,value:i})=>{if(!r){let a=(e?i:i.map(l=>encodeURIComponent(l))).join(W(c));switch(c){case"label":return`.${a}`;case"matrix":return`;${t}=${a}`;case"simple":return a;default:return`${t}=${a}`}}let y=O(c),n=i.map(a=>c==="label"||c==="simple"?e?a:encodeURIComponent(a):g({allowReserved:e,name:t,value:a})).join(y);return c==="label"||c==="matrix"?y+n:n},g=({allowReserved:e,name:r,value:t})=>{if(t==null)return"";if(typeof t=="object")throw new Error("Deeply-nested arrays/objects aren’t supported. Provide your own `querySerializer()` to handle these.");return`${r}=${e?t:encodeURIComponent(t)}`},k=({allowReserved:e,explode:r,name:t,style:c,value:i})=>{if(i instanceof Date)return`${t}=${i.toISOString()}`;if(c!=="deepObject"&&!r){let a=[];Object.entries(i).forEach(([o,u])=>{a=[...a,o,e?u:encodeURIComponent(u)]});let l=a.join(",");switch(c){case"form":return`${t}=${l}`;case"label":return`.${l}`;case"matrix":return`;${t}=${l}`;default:return l}}let y=$(c),n=Object.entries(i).map(([a,l])=>g({allowReserved:e,name:c==="deepObject"?`${t}[${a}]`:a,value:l})).join(y);return c==="label"||c==="matrix"?y+n:n},R=/\{[^{}]+\}/g,S=({path:e,url:r})=>{let t=r,c=r.match(R);if(c)for(let i of c){let y=!1,n=i.substring(1,i.length-1),a="simple";n.endsWith("*")&&(y=!0,n=n.substring(0,n.length-1)),n.startsWith(".")?(n=n.substring(1),a="label"):n.startsWith(";")&&(n=n.substring(1),a="matrix");let l=e[n];if(l==null)continue;if(Array.isArray(l)){t=t.replace(i,q({explode:y,name:n,style:a,value:l}));continue}if(typeof l=="object"){t=t.replace(i,k({explode:y,name:n,style:a,value:l}));continue}if(a==="matrix"){t=t.replace(i,`;${g({name:n,value:l})}`);continue}let o=encodeURIComponent(a==="label"?`.${l}`:l);t=t.replace(i,o)}return t},I=({allowReserved:e,array:r,object:t}={})=>c=>{let i=[];if(c&&typeof c=="object")for(let y in c){let n=c[y];if(n!=null){if(Array.isArray(n)){i=[...i,q({allowReserved:e,explode:!0,name:y,style:"form",value:n,...r})];continue}if(typeof n=="object"){i=[...i,k({allowReserved:e,explode:!0,name:y,style:"deepObject",value:n,...t})];continue}i=[...i,g({allowReserved:e,name:y,value:n})]}}return i.join("&")},N=e=>{var t;if(!e)return"stream";let r=(t=e.split(";")[0])==null?void 0:t.trim();if(r){if(r.startsWith("application/json")||r.endsWith("+json"))return"json";if(r==="multipart/form-data")return"formData";if(["application/","audio/","image/","video/"].some(c=>r.startsWith(c)))return"blob";if(r.startsWith("text/"))return"text"}},E=async({security:e,...r})=>{for(let t of e){let c=await _(t,r.auth);if(!c)continue;let i=t.name??"Authorization";switch(t.in){case"query":r.query||(r.query={}),r.query[i]=c;break;case"cookie":r.headers.append("Cookie",`${i}=${c}`);break;case"header":default:r.headers.set(i,c);break}return}},x=e=>U({baseUrl:e.baseUrl,path:e.path,query:e.query,querySerializer:typeof e.querySerializer=="function"?e.querySerializer:I(e.querySerializer),url:e.url}),U=({baseUrl:e,path:r,query:t,querySerializer:c,url:i})=>{let y=i.startsWith("/")?i:`/${i}`,n=(e??"")+y;r&&(n=S({path:r,url:n}));let a=t?c(t):"";return a.startsWith("?")&&(a=a.substring(1)),a&&(n+=`?${a}`),n},w=(e,r)=>{var c;let t={...e,...r};return(c=t.baseUrl)!=null&&c.endsWith("/")&&(t.baseUrl=t.baseUrl.substring(0,t.baseUrl.length-1)),t.headers=A(e.headers,r.headers),t},A=(...e)=>{let r=new Headers;for(let t of e){if(!t||typeof t!="object")continue;let c=t instanceof Headers?t.entries():Object.entries(t);for(let[i,y]of c)if(y===null)r.delete(i);else if(Array.isArray(y))for(let n of y)r.append(i,n);else y!==void 0&&r.set(i,typeof y=="object"?JSON.stringify(y):y)}return r},K=class{constructor(){v(this,"_fns");this._fns=[]}clear(){this._fns=[]}exists(e){return this._fns.indexOf(e)!==-1}eject(e){let r=this._fns.indexOf(e);r!==-1&&(this._fns=[...this._fns.slice(0,r),...this._fns.slice(r+1)])}use(e){this._fns=[...this._fns,e]}},z=()=>({error:new K,request:new K,response:new K}),D=I({allowReserved:!1,array:{explode:!0,style:"form"},object:{explode:!0,style:"deepObject"}}),P={"Content-Type":"application/json"},T=(e={})=>({...j,headers:P,parseAs:"auto",querySerializer:D,...e}),H=(e={})=>{let r=w(T(),e),t=()=>({...r}),c=n=>(r=w(r,n),t()),i=z(),y=async n=>{let a={...r,...n,fetch:n.fetch??r.fetch??globalThis.fetch,headers:A(r.headers,n.headers)};a.security&&await E({...a,security:a.security}),a.body&&a.bodySerializer&&(a.body=a.bodySerializer(a.body)),(a.body===void 0||a.body==="")&&a.headers.delete("Content-Type");let l=x(a),o={redirect:"follow",...a},u=new Request(l,o);for(let p of i.request._fns)u=await p(u,a);let V=a.fetch,d=await V(u);for(let p of i.response._fns)d=await p(d,u,a);let m={request:u,response:d};if(d.ok){if(d.status===204||d.headers.get("Content-Length")==="0")return{data:{},...m};let p=(a.parseAs==="auto"?N(d.headers.get("Content-Type")):a.parseAs)??"json";if(p==="stream")return{data:d.body,...m};let b=await d[p]();return p==="json"&&(a.responseValidator&&await a.responseValidator(b),a.responseTransformer&&(b=await a.responseTransformer(b))),{data:b,...m}}let f=await d.text();try{f=JSON.parse(f)}catch{}let h=f;for(let p of i.error._fns)h=await p(f,d,u,a);if(h=h||{},a.throwOnError)throw h;return{error:h,...m}};return{buildUrl:x,connect:n=>y({...n,method:"CONNECT"}),delete:n=>y({...n,method:"DELETE"}),get:n=>y({...n,method:"GET"}),getConfig:t,head:n=>y({...n,method:"HEAD"}),interceptors:i,options:n=>y({...n,method:"OPTIONS"}),patch:n=>y({...n,method:"PATCH"}),post:n=>y({...n,method:"POST"}),put:n=>y({...n,method:"PUT"}),request:y,setConfig:c,trace:n=>y({...n,method:"TRACE"})}};const s=H(T()),L=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks",...e}),M=e=>((e==null?void 0:e.client)??s).post({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks",...e,headers:{"Content-Type":"application/json",...e==null?void 0:e.headers}}),G=e=>(e.client??s).delete({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks/{webhook_id}",...e}),Q=e=>(e.client??s).patch({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks/{webhook_id}",...e,headers:{"Content-Type":"application/json",...e==null?void 0:e.headers}}),F=e=>(e.client??s).post({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks/{webhook_id}/verify",...e}),X=e=>(e.client??s).post({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks/{webhook_id}/resend-otp",...e}),Y=e=>((e==null?void 0:e.client)??s).post({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/webhooks/test",...e,headers:{"Content-Type":"application/json",...e==null?void 0:e.headers}}),Z=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/events",...e}),ee=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/events/{contractAddress}",...e}),te=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/events/{contractAddress}/{signature}",...e}),re=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/transactions",...e}),ne=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/transactions/{contractAddress}",...e}),ae=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/transactions/{contractAddress}/{signature}",...e}),se=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/transfers/transaction/{transaction_hash}",...e}),ie=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/transfers/{contract_address}",...e}),ce=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/transfers",...e}),ye=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/erc20/{ownerAddress}",...e}),le=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/erc721/{ownerAddress}",...e}),de=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/erc1155/{ownerAddress}",...e}),ue=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/price/supported",...e}),pe=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/price",...e}),oe=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/tokens/lookup",...e}),he=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/resolve/{input}",...e}),me=e=>((e==null?void 0:e.client)??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/blocks",...e}),fe=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/contracts/abi/{contractAddress}",...e}),be=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/contracts/metadata/{contractAddress}",...e}),ge=e=>(e.client??s).post({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/decode/{contractAddress}",...e,headers:{"Content-Type":"application/json",...e==null?void 0:e.headers}}),Ke=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/balance/{ownerAddress}",...e}),ve=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/collections/{contract_address}",...e}),xe=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts",...e}),we=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/owners/{contract_address}",...e}),qe=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/owners/{contract_address}/{token_id}",...e}),ke=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/transfers",...e}),Ie=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/transfers/transaction/{transaction_hash}",...e}),Ae=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/transfers/{contract_address}",...e}),Te=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/{contract_address}",...e}),Ve=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/transfers/{contract_address}/{token_id}",...e}),Ce=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/{contract_address}/{token_id}",...e}),Be=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/metadata/refresh/{contract_address}",...e}),_e=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/nfts/metadata/refresh/{contract_address}/{token_id}",...e}),je=e=>(e.client??s).get({security:[{name:"x-client-id",type:"apiKey"},{scheme:"bearer",type:"http"},{in:"query",name:"clientId",type:"apiKey"}],url:"/v1/wallets/{wallet_address}/transactions",...e});function Oe(e){s.setConfig({headers:{...e.clientId&&{"x-client-id":e.clientId},...e.secretKey&&{"x-api-key":e.secretKey}},...e.override??{}})}export{Oe as configure,G as deleteV1WebhooksByWebhookId,me as getV1Blocks,fe as getV1ContractsAbiByContractAddress,be as getV1ContractsMetadataByContractAddress,Z as getV1Events,ee as getV1EventsByContractAddress,te as getV1EventsByContractAddressBySignature,xe as getV1Nfts,Ke as getV1NftsBalanceByOwnerAddress,Te as getV1NftsByContractAddress,Ce as getV1NftsByContractAddressByTokenId,ve as getV1NftsCollectionsByContractAddress,Be as getV1NftsMetadataRefreshByContractAddress,_e as getV1NftsMetadataRefreshByContractAddressByTokenId,we as getV1NftsOwnersByContractAddress,qe as getV1NftsOwnersByContractAddressByTokenId,ke as getV1NftsTransfers,Ae as getV1NftsTransfersByContractAddress,Ve as getV1NftsTransfersByContractAddressByTokenId,Ie as getV1NftsTransfersTransactionByTransactionHash,he as getV1ResolveByInput,de as getV1TokensErc1155ByOwnerAddress,ye as getV1TokensErc20ByOwnerAddress,le as getV1TokensErc721ByOwnerAddress,oe as getV1TokensLookup,pe as getV1TokensPrice,ue as getV1TokensPriceSupported,ce as getV1TokensTransfers,ie as getV1TokensTransfersByContractAddress,se as getV1TokensTransfersTransactionByTransactionHash,re as getV1Transactions,ne as getV1TransactionsByContractAddress,ae as getV1TransactionsByContractAddressBySignature,je as getV1WalletsByWalletAddressTransactions,L as getV1Webhooks,Q as patchV1WebhooksByWebhookId,ge as postV1DecodeByContractAddress,M as postV1Webhooks,X as postV1WebhooksByWebhookIdResendOtp,F as postV1WebhooksByWebhookIdVerify,Y as postV1WebhooksTest};
