const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/switch-chain-BoIZ2mZU.js","assets/index-BLCiG1f1.js","assets/index-DcbjDVZw.css"])))=>i.map(i=>d[i]);
import{eY as l,bR as T,j as d,N as y,L as R,eZ as b,_ as v}from"./index-BLCiG1f1.js";async function C(i){const{account:t,chainId:e,params:[p]}=i;if(!t.sendRawTransaction)throw new Error("The current account does not support sending raw transactions");return(await t.sendRawTransaction({rawTransaction:p,chainId:e})).transactionHash}function m(i,t){if(l(i.address)!==l(t))throw new Error(`Failed to validate account address (${i.address}), differs from ${t}`)}function w(i){const t=i.split(":"),e=Number.parseInt(t[1]??"0");if(t.length!==2||t[0]!=="eip155"||e===0||!e)throw new Error(`Invalid chainId ${i}, should have the format 'eip155:1'`);return e}async function E(i){const{account:t,chainId:e,thirdwebClient:p,params:[c]}=i;c.from!==void 0&&m(t,c.from);const h=T({gas:c.gas?d(c.gas):void 0,gasPrice:c.gasPrice?d(c.gasPrice):void 0,value:c.value?d(c.value):void 0,to:c.to,data:c.data,chain:y(e),client:p});return(await R({transaction:h,account:t})).transactionHash}async function I(i){const{account:t,params:[e]}=i;if(!t.signTransaction)throw new Error("The current account does not support signing transactions");return e.from!==void 0&&m(t,e.from),t.signTransaction({gas:e.gas?d(e.gas):void 0,gasPrice:e.gasPrice?d(e.gasPrice):void 0,value:e.value?d(e.value):void 0,nonce:e.nonce?b(e.nonce):void 0,to:e.to,data:e.data})}async function f(i){const{account:t,params:e}=i;return m(t,e[0]),t.signTypedData(typeof e[1]=="string"?JSON.parse(e[1]):e[1])}async function g(i){const{account:t,params:e}=i;return m(t,e[1]),t.signMessage({message:{raw:e[0]}})}async function q(i){const{wallet:t,walletConnectClient:e,thirdwebClient:p,event:{topic:c,id:h,params:{chainId:u,request:n}},handlers:a}=i,o=t.getAccount();if(!o)throw new Error("No account connected to provided wallet");let s;try{switch(n.method){case"personal_sign":{a!=null&&a.personal_sign?s=await a.personal_sign({account:o,params:n.params}):s=await g({account:o,params:n.params});break}case"eth_sign":{a!=null&&a.eth_sign?s=await a.eth_sign({account:o,params:n.params}):s=await g({account:o,params:n.params});break}case"eth_signTypedData":{a!=null&&a.eth_signTypedData?s=await a.eth_signTypedData({account:o,params:n.params}):s=await f({account:o,params:n.params});break}case"eth_signTypedData_v4":{a!=null&&a.eth_signTypedData_v4?s=await a.eth_signTypedData_v4({account:o,params:n.params}):s=await f({account:o,params:n.params});break}case"eth_signTransaction":{a!=null&&a.eth_signTransaction?s=await a.eth_signTransaction({account:o,params:n.params}):s=await I({account:o,params:n.params});break}case"eth_sendTransaction":{const r=w(u);a!=null&&a.eth_sendTransaction?s=await a.eth_sendTransaction({account:o,chainId:r,params:n.params}):s=await E({account:o,chainId:r,thirdwebClient:p,params:n.params});break}case"eth_sendRawTransaction":{const r=w(u);a!=null&&a.eth_sendRawTransaction?s=await a.eth_sendRawTransaction({account:o,chainId:r,params:n.params}):s=await C({account:o,chainId:r,params:n.params});break}case"wallet_addEthereumChain":{if(a!=null&&a.wallet_addEthereumChain)s=await a.wallet_addEthereumChain({wallet:t,params:n.params});else throw new Error("Unsupported request method: wallet_addEthereumChain");break}case"wallet_switchEthereumChain":{if(a!=null&&a.wallet_switchEthereumChain)s=await a.wallet_switchEthereumChain({wallet:t,params:n.params});else{const{handleSwitchChain:r}=await v(async()=>{const{handleSwitchChain:_}=await import("./switch-chain-BoIZ2mZU.js");return{handleSwitchChain:_}},__vite__mapDeps([0,1,2]));s=await r({wallet:t,params:n.params})}break}default:{const r=a==null?void 0:a[n.method];if(r)s=await r({account:o,chainId:w(u),params:n.params});else throw new Error(`Unsupported request method: ${n.method}`)}}}catch(r){s={code:typeof r=="object"&&r!==null&&"code"in r?r.code:500,message:typeof r=="object"&&r!==null&&"message"in r?r.message:"Unknown error"}}e.respond({topic:c,response:{id:h,jsonrpc:"2.0",result:s}})}export{q as fulfillRequest};
