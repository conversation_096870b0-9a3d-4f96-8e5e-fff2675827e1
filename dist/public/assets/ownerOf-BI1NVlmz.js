import{z as o,d0 as c,r as d}from"./index-BLCiG1f1.js";import{d as a}from"./detectExtension-CzSaTPm3.js";const t="0x6352211e",n=[{type:"uint256",name:"tokenId"}],r=[{type:"address"}];function i(e){return a({availableSelectors:e,method:[t,n,r]})}function s(e){return o(n,[e.tokenId])}function m(e){return t+s(e).slice(2)}function O(e){return c(r,e)[0]}async function p(e){return d({contract:e.contract,method:[t,n,r],params:[e.tokenId]})}export{t as FN_SELECTOR,O as decodeOwnerOfResult,m as encodeOwnerOf,s as encodeOwnerOfParams,i as isOwnerOfSupported,p as ownerOf};
