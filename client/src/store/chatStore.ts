import { create } from 'zustand';
import { Chat, Message } from '@shared/schema';

interface ChatState {
  chats: Chat[];
  activeChat: Chat | null;
  messages: Message[];
  isLoading: boolean;
  
  // Actions
  setChats: (chats: Chat[]) => void;
  setActiveChat: (chat: Chat | null) => void;
  createNewChat: (chat: Chat) => void;
  deleteChat: (chatId: number) => void;
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  setLoading: (isLoading: boolean) => void;
}

export const useChatStore = create<ChatState>((set) => ({
  chats: [],
  activeChat: null,
  messages: [],
  isLoading: false,
  
  setChats: (chats) => set({ chats }),
  
  setActiveChat: (chat) => set({ activeChat: chat }),
  
  createNewChat: (chat) => set((state) => ({
    chats: [chat, ...state.chats],
    activeChat: chat
  })),
  
  deleteChat: (chatId) => set((state) => {
    const newChats = state.chats.filter(chat => chat.id !== chatId);
    const newActiveChat = state.activeChat?.id === chatId
      ? newChats[0] || null
      : state.activeChat;
      
    return {
      chats: newChats,
      activeChat: newActiveChat
    };
  }),
  
  setMessages: (messages) => set({ messages }),
  
  addMessage: (message) => set((state) => ({
    messages: [...state.messages, message]
  })),
  
  setLoading: (isLoading) => set({ isLoading })
}));
