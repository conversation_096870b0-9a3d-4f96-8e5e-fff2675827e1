import { ThirdwebSDK } from "@thirdweb-dev/sdk";

/**
 * Get gas prices for a specific chain
 */
export async function getGasPrices(chainId: string): Promise<{
  low: number;
  average: number;
  high: number;
}> {
  try {
    const sdk = new ThirdwebSDK(chainId);
    const provider = sdk.getProvider();
    const feeData = await provider.getFeeData();
    
    // Convert from wei to gwei
    const gweiFactor = 1e9;
    const lowGwei = Math.floor(Number(feeData.gasPrice) / gweiFactor);
    
    return {
      low: lowGwei,
      average: Math.floor(lowGwei * 1.2),
      high: Math.floor(lowGwei * 1.5)
    };
  } catch (error) {
    console.error("Failed to get gas prices:", error);
    return {
      low: 30,
      average: 45,
      high: 60
    };
  }
}

/**
 * Estimate gas for a transaction
 */
export async function estimateGas(
  chainId: string,
  toAddress: string,
  value: string
): Promise<number> {
  try {
    const sdk = new ThirdwebSDK(chainId);
    const provider = sdk.getProvider();
    
    const gasEstimate = await provider.estimateGas({
      to: toAddress,
      value: value
    });
    
    return Number(gasEstimate);
  } catch (error) {
    console.error("Failed to estimate gas:", error);
    throw error;
  }
}

/**
 * Get token balance for an address
 */
export async function getTokenBalance(
  chainId: string,
  tokenAddress: string,
  walletAddress: string
): Promise<{
  balance: string;
  symbol: string;
  decimals: number;
}> {
  try {
    const sdk = new ThirdwebSDK(chainId);
    const contract = await sdk.getContract(tokenAddress);
    
    const balance = await contract.erc20.balanceOf(walletAddress);
    const symbol = await contract.erc20.symbol();
    const decimals = await contract.erc20.decimals();
    
    return {
      balance: balance.displayValue,
      symbol,
      decimals
    };
  } catch (error) {
    console.error("Failed to get token balance:", error);
    throw error;
  }
}

/**
 * Get contract information
 */
export async function getContractInfo(
  chainId: string,
  contractAddress: string
): Promise<{
  name?: string;
  symbol?: string;
  totalSupply?: string;
  isERC20: boolean;
  isERC721: boolean;
  isERC1155: boolean;
}> {
  try {
    const sdk = new ThirdwebSDK(chainId);
    const contract = await sdk.getContract(contractAddress);
    
    // Check contract type
    const isERC20 = await contract.hasERC20();
    const isERC721 = await contract.hasERC721();
    const isERC1155 = await contract.hasERC1155();
    
    let result: any = {
      isERC20,
      isERC721,
      isERC1155
    };
    
    // Get token info if it's ERC20
    if (isERC20) {
      result.name = await contract.erc20.name();
      result.symbol = await contract.erc20.symbol();
      result.totalSupply = (await contract.erc20.totalSupply()).displayValue;
    }
    
    // Get NFT info if it's ERC721
    if (isERC721) {
      result.name = await contract.erc721.name();
      result.symbol = await contract.erc721.symbol();
      result.totalSupply = (await contract.erc721.totalCount()).toString();
    }
    
    return result;
  } catch (error) {
    console.error("Failed to get contract info:", error);
    throw error;
  }
}
