import { createThirdwebClient } from "thirdweb";
import { createWallet } from "thirdweb/wallets";
import { ethereum, polygon, sepolia, polygonAmoy } from "thirdweb/chains";

// Get the client ID from environment variables
const clientId = import.meta.env.VITE_THIRDWEB_CLIENT_ID;

if (!clientId) {
  throw new Error("VITE_THIRDWEB_CLIENT_ID is not set in environment variables");
}

// Create the shared Thirdweb client
export const client = createThirdwebClient({
  clientId,
});

// Define supported wallets
export const wallets = [
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("walletConnect"),
];

// Define supported chains (including testnet chains as per user preference)
export const chains = [ethereum, polygon, sepolia, polygonAmoy];

// Export individual chains for easy access
export { ethereum, polygon, sepolia, polygonAmoy };