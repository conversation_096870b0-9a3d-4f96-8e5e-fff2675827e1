// Singleton session manager to prevent multiple session creations
class SessionManager {
  private static instance: SessionManager;
  private sessionId: string | null = null;
  private isCreating: boolean = false;
  private createPromise: Promise<string | null> | null = null;

  private constructor() {
    // Try to get existing session from localStorage
    this.sessionId = localStorage.getItem("nebula_session_id");
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  public getSessionId(): string | null {
    return this.sessionId;
  }

  public async ensureSession(): Promise<string | null> {
    // If we already have a session, return it
    if (this.sessionId) {
      return this.sessionId;
    }

    // If we're already creating a session, wait for it
    if (this.createPromise) {
      return this.createPromise;
    }

    // Create a new session
    this.createPromise = this.createSession();
    const result = await this.createPromise;
    this.createPromise = null;
    return result;
  }

  private async createSession(): Promise<string | null> {
    if (this.sessionId || this.isCreating) {
      return this.sessionId;
    }

    this.isCreating = true;

    try {
      const response = await fetch("/api/nebula/create-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: "Hello, I am ready to chat about blockchain development!",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.sessionId = data.sessionId;
        localStorage.setItem("nebula_session_id", data.sessionId);
        console.log("Nebula session created:", data.sessionId);
        return data.sessionId;
      } else {
        console.error("Failed to create Nebula session");
        return null;
      }
    } catch (error) {
      console.error("Error creating Nebula session:", error);
      return null;
    } finally {
      this.isCreating = false;
    }
  }

  public clearSession(): void {
    this.sessionId = null;
    localStorage.removeItem("nebula_session_id");
  }
}

export const sessionManager = SessionManager.getInstance();
