export interface Chain {
  id: number;
  name: string;
  chainId: string;
  rpcUrl: string;
  icon: string;
  isTestnet: boolean;
}

export interface ChatMessage {
  id: number;
  chatId: number;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: {
    chainId?: string;
    source?: string;
    executionTime?: number;
    blockchainData?: any;
  };
}

export interface LLMResponse {
  text: string;
  source: string;
  executionTime: number;
  blockchainData?: any;
}

export interface WalletInfo {
  address: string;
  balance: string;
  chainId: string;
}

export interface ChainOption {
  id: string;
  name: string;
  chainId: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  color: string;
}
