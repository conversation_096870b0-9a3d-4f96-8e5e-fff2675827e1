import { useState, useEffect, useCallback } from 'react';
import { useSDK, useAddress } from '@thirdweb-dev/react';
import { 
  ChatThread, 
  ChatMessage, 
  getChatThreads, 
  getChatMessages, 
  sendChatMessage, 
  createChatThread 
} from '@/lib/chatClient';
import { toast } from '@/hooks/use-toast';

export function useThirdwebChat() {
  const sdk = useSDK();
  const address = useAddress();
  const [threads, setThreads] = useState<ChatThread[]>([]);
  const [activeThread, setActiveThread] = useState<ChatThread | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState({
    threads: false,
    messages: false,
    sendMessage: false,
    createThread: false
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch all threads for the connected wallet
  const fetchThreads = useCallback(async () => {
    if (!sdk || !address) return;
    
    setLoading(prev => ({ ...prev, threads: true }));
    setError(null);
    
    try {
      const fetchedThreads = await getChatThreads(sdk);
      setThreads(fetchedThreads);
    } catch (err: any) {
      console.error("Error fetching threads:", err);
      setError(err.message || "Failed to fetch chat threads");
      toast({
        title: "Error fetching chats",
        description: err.message || "Failed to fetch chat threads",
        variant: "destructive"
      });
    } finally {
      setLoading(prev => ({ ...prev, threads: false }));
    }
  }, [sdk, address]);

  // Fetch messages for a specific thread
  const fetchMessages = useCallback(async (threadId: string) => {
    if (!sdk || !address || !threadId) return;
    
    setLoading(prev => ({ ...prev, messages: true }));
    setError(null);
    
    try {
      const fetchedMessages = await getChatMessages(sdk, threadId);
      setMessages(fetchedMessages);
    } catch (err: any) {
      console.error("Error fetching messages:", err);
      setError(err.message || "Failed to fetch chat messages");
      toast({
        title: "Error fetching messages",
        description: err.message || "Failed to fetch chat messages",
        variant: "destructive"
      });
    } finally {
      setLoading(prev => ({ ...prev, messages: false }));
    }
  }, [sdk, address]);

  // Send a message to the active thread
  const sendMessage = useCallback(async (content: string, metadata?: Record<string, any>) => {
    if (!sdk || !address || !activeThread?.id || !content.trim()) return;
    
    setLoading(prev => ({ ...prev, sendMessage: true }));
    setError(null);
    
    try {
      const sentMessage = await sendChatMessage(sdk, activeThread.id, content, metadata);
      
      // Optimistically update the UI
      setMessages(prev => [...prev, sentMessage]);
      
      return sentMessage;
    } catch (err: any) {
      console.error("Error sending message:", err);
      setError(err.message || "Failed to send message");
      toast({
        title: "Error sending message",
        description: err.message || "Failed to send message",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(prev => ({ ...prev, sendMessage: false }));
    }
  }, [sdk, address, activeThread]);

  // Create a new chat thread
  const createThread = useCallback(async (name: string, topic?: string, metadata?: Record<string, any>) => {
    if (!sdk || !address) return null;
    
    setLoading(prev => ({ ...prev, createThread: true }));
    setError(null);
    
    try {
      const newThread = await createChatThread(sdk, name, topic, metadata);
      
      // Update the list of threads
      setThreads(prev => [newThread, ...prev]);
      
      // Set as active thread
      setActiveThread(newThread);
      
      toast({
        title: "Chat created",
        description: "New chat thread created successfully",
      });
      
      return newThread;
    } catch (err: any) {
      console.error("Error creating thread:", err);
      setError(err.message || "Failed to create chat thread");
      toast({
        title: "Error creating chat",
        description: err.message || "Failed to create chat thread",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(prev => ({ ...prev, createThread: false }));
    }
  }, [sdk, address]);

  // Set active thread and fetch its messages
  const setActiveThreadAndFetchMessages = useCallback((thread: ChatThread) => {
    setActiveThread(thread);
    fetchMessages(thread.id);
  }, [fetchMessages]);

  // Automatically fetch threads when SDK and address are available
  useEffect(() => {
    if (sdk && address) {
      fetchThreads();
    } else {
      // Reset state when disconnected
      setThreads([]);
      setActiveThread(null);
      setMessages([]);
    }
  }, [sdk, address, fetchThreads]);

  // Fetch messages whenever active thread changes
  useEffect(() => {
    if (activeThread) {
      fetchMessages(activeThread.id);
    }
  }, [activeThread, fetchMessages]);

  return {
    threads,
    messages,
    activeThread,
    loading,
    error,
    fetchThreads,
    fetchMessages,
    sendMessage,
    createThread,
    setActiveThread: setActiveThreadAndFetchMessages,
  };
}