import { useState, useEffect } from "react";
import { sessionManager } from "@/lib/sessionManager";

export function useNebulaSession() {
  const [sessionId, setSessionId] = useState<string | null>(() => {
    return sessionManager.getSessionId();
  });
  const [isCreatingSession, setIsCreatingSession] = useState(false);

  // Ensure session exists before performing operations
  const ensureSession = async () => {
    setIsCreatingSession(true);
    try {
      const session = await sessionManager.ensureSession();
      setSessionId(session);
      return session;
    } finally {
      setIsCreatingSession(false);
    }
  };

  // Legacy function for backward compatibility
  const createNebulaSession = async () => {
    return await ensureSession();
  };

  return {
    sessionId,
    isCreatingSession,
    createNebulaSession,
    ensureSession,
  };
}
