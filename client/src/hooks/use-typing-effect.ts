import { useState, useEffect, useRef } from "react";

export function useTypingEffect(text: string, speed: number = 7) {
  const [displayedText, setDisplayedText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const showFullText = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setDisplayedText(text);
    setIsTyping(false);
  };

  useEffect(() => {
    if (!text) {
      setDisplayedText("");
      return;
    }

    setIsTyping(true);
    setDisplayedText("");

    let index = 0;
    timerRef.current = setInterval(() => {
      if (index < text.length) {
        setDisplayedText(text.slice(0, index + 1));
        index++;
      } else {
        setIsTyping(false);
        clearInterval(timerRef.current!);
      }
    }, speed);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      setIsTyping(false);
    };
  }, [text, speed]);

  return { displayedText, isTyping, showFullText };
}
