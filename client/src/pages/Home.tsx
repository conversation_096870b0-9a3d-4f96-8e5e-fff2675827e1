import { useActiveAccount } from "thirdweb/react";
import LoggedInHome from "@/components/LoggedInHome";
import LoggedOutHome from "@/components/LoggedOutHome";

const Home = () => {
  // Check if user is signed in (has an active account)
  const account = useActiveAccount();
  const isSignedIn = !!account?.address;

  // Render appropriate component based on authentication status
  if (isSignedIn) {
    return <LoggedInHome />;
  }

  return <LoggedOutHome />;
};

export default Home;
