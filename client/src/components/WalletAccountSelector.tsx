import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  ChevronDownIcon,
  WalletIcon,
  CheckIcon,
  LogOutIcon,
} from "lucide-react";
import {
  useActiveWallet,
  useActiveAccount,
  useDisconnect,
} from "thirdweb/react";
import { toast } from "@/hooks/use-toast";

interface WalletAccountSelectorProps {
  className?: string;
}

const WalletAccountSelector = ({ className }: WalletAccountSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const wallet = useActiveWallet();
  const account = useActiveAccount();
  const { disconnect } = useDisconnect();

  // Format address for display
  const formatAddress = (address: string) => {
    if (!address) return "";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // Get wallet type/name
  const getWalletType = (wallet: any) => {
    if (!wallet) return "Unknown";

    // Check for common wallet types
    if (wallet.id === "io.metamask") return "MetaMask";
    if (wallet.id === "com.coinbase.wallet") return "Coinbase";
    if (wallet.id === "walletConnect") return "WalletConnect";
    if (wallet.id?.includes("smart")) return "Smart Account";

    // Fallback to wallet name or id
    return wallet.name || wallet.id || "Wallet";
  };

  // Handle disconnect
  const handleDisconnect = async () => {
    try {
      await disconnect(wallet);
      setIsOpen(false);
      toast({
        title: "Wallet disconnected",
        description: "Your wallet has been disconnected successfully",
      });
    } catch (error) {
      console.error("Error disconnecting wallet:", error);
      toast({
        title: "Error",
        description: "Failed to disconnect wallet",
        variant: "destructive",
      });
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Don't render if no wallet is connected
  if (!wallet || !account?.address) {
    return null;
  }

  const walletType = getWalletType(wallet);
  const formattedAddress = formatAddress(account.address);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        className="text-[0.6rem] sm:text-xs flex items-center gap-0.5 sm:gap-1 text-muted-foreground hover:text-foreground px-1 sm:px-2 py-1 h-auto rounded-md bg-muted/20 border border-border/30 hover:bg-primary/10 hover:border-primary/40 hover:shadow-[0_2px_8px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none focus-visible:outline-none min-h-[26px] sm:min-h-auto"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
        <span>{formattedAddress}</span>
        <ChevronDownIcon className="h-2.5 w-2.5" />
      </Button>

      {isOpen && (
        <div className="absolute top-full mt-1 left-0 min-w-[300px] bg-background border border-border rounded-lg shadow-lg z-[100]">
          {/* Header */}
          <div className="p-3 border-b border-border">
            <p className="text-xs text-muted-foreground mb-1">
              Select account to use for executing transactions
            </p>
          </div>

          {/* Current Account */}
          <div className="p-3">
            <div className="flex items-center gap-3 p-3 rounded-lg cursor-pointer hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <WalletIcon className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {formattedAddress}
                  </span>
                  <CheckIcon className="h-4 w-4 text-primary" />
                </div>
                <p className="text-xs text-muted-foreground">{walletType}</p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="p-3 border-t border-border">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs px-3 py-2 h-auto text-muted-foreground hover:text-foreground hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
              onClick={handleDisconnect}
            >
              <LogOutIcon className="h-3 w-3 mr-2" />
              Disconnect Wallet
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default WalletAccountSelector;
