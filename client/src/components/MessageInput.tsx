import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  SendIcon,
  PaperclipIcon,
  LinkIcon,
  ChevronDownIcon,
  ArrowUpIcon,
} from "lucide-react";
import ChainSelector from "@/components/ChainSelector";
import WalletAccountSelector from "@/components/WalletAccountSelector";
import { useActiveWallet, useActiveWalletChain } from "thirdweb/react";
import { supportedChains, getChainById } from "@/lib/chainConfig";

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  compact?: boolean;
}

const MessageInput = ({
  onSendMessage,
  isLoading = false,
  compact = false,
}: MessageInputProps) => {
  const [message, setMessage] = useState("");
  const [showChainSelector, setShowChainSelector] = useState(false);
  const wallet = useActiveWallet();
  const activeChain = useActiveWalletChain();

  // Get current chain from wallet or default to Ethereum
  const currentChainId = activeChain?.id ? activeChain.id.toString() : "1";
  const currentChain = getChainById(currentChainId) || supportedChains[0];
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current && !compact) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message, compact]);

  const handleSend = () => {
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      setMessage("");

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const toggleChainSelector = () => {
    setShowChainSelector((prev) => !prev);
  };

  if (compact) {
    return (
      <div className="relative">
        <Input
          type="text"
          placeholder={wallet ? "Ask Nebula..." : "Connect wallet to chat"}
          className="w-full bg-muted theme-input py-3 pl-4 pr-12 sm:pr-10 text-sm sm:text-base"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isLoading || !wallet}
        />
        <Button
          className="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 text-primary bg-transparent hover:bg-transparent p-1 sm:p-0 min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto"
          onClick={handleSend}
          disabled={isLoading || !message.trim() || !wallet}
        >
          <SendIcon className="h-4 w-4" />
        </Button>
        <div className="absolute top-full mt-2 right-0">
          <div className="bg-background p-1 rounded-md shadow-md border border-border">
            <Button
              variant="ghost"
              size="sm"
              className="p-2 text-muted-foreground hover:text-primary min-h-[44px] min-w-[44px]"
              onClick={toggleChainSelector}
              title="Select Blockchain"
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-2 text-muted-foreground hover:text-primary min-h-[44px] min-w-[44px]"
              title="Upload File"
            >
              <PaperclipIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full mx-auto">
      {/* Container that looks like a single textarea */}
      <div
        className="relative bg-muted/30 theme-input rounded-md border outline-none focus:outline-none focus-visible:outline-none ring-1 ring-gray-200/20 dark:ring-gray-700/20"
        tabIndex={0}
        onFocus={() => {
          textareaRef.current?.focus();
        }}
        onBlur={() => {}}
        onClick={() => textareaRef.current?.focus()}
      >
        {/* Actual textarea for text input */}
        <Textarea
          ref={textareaRef}
          rows={1}
          placeholder={
            wallet ? "Ask Web3AI" : "Connect your wallet to start chatting"
          }
          className="w-full bg-transparent border-0 py-3 pl-4 pr-12 sm:pr-16 text-sm sm:text-base resize-none min-h-[50px] sm:min-h-[60px] max-h-[120px] overflow-y-auto outline-none focus:outline-none focus-visible:outline-none focus:ring-0 focus:border-0"
          style={{
            scrollbarWidth: "thin",
            scrollbarColor: "rgba(255, 255, 255, 0.1) transparent",
            outline: "none !important",
            border: "none !important",
            boxShadow: "none !important",
          }}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {}}
          onBlur={() => {}}
          disabled={isLoading || !wallet}
        />

        {/* Bottom section for wallet and chain components */}
        <div className="px-3 sm:px-4 pb-3 pt-3 flex items-center justify-between min-h-[32px] sm:min-h-[36px] relative z-10 gap-2">
          <div className="flex items-center gap-1 sm:gap-2 flex-1 overflow-visible">
            <div className="flex-shrink-0 relative z-20">
              <WalletAccountSelector />
            </div>
            {wallet && (
              <div className="relative flex-shrink-0 z-20">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-[0.6rem] sm:text-xs flex items-center gap-0.5 sm:gap-1 text-muted-foreground hover:text-foreground px-1 sm:px-2 py-1 h-auto rounded-md bg-muted/20 border border-border/30 hover:bg-primary/10 hover:border-primary/40 hover:shadow-[0_2px_8px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none focus-visible:outline-none min-h-[26px] sm:min-h-auto"
                  onClick={toggleChainSelector}
                >
                  <div
                    className={`w-1.5 h-1.5 rounded-full`}
                    style={{ backgroundColor: currentChain.color }}
                  ></div>
                  <span className="whitespace-nowrap hidden sm:inline">
                    {currentChain.name}
                  </span>
                  <span className="whitespace-nowrap sm:hidden">
                    {currentChain.name.slice(0, 8)}...
                  </span>
                  <ChevronDownIcon className="h-2.5 w-2.5" />
                </Button>

                {showChainSelector && (
                  <ChainSelector onClose={() => setShowChainSelector(false)} />
                )}
              </div>
            )}
          </div>

          {/* Send button */}
          <div className="flex-shrink-0">
            <Button
              className="text-primary bg-transparent hover:bg-transparent p-1 sm:p-0 focus:outline-none focus-visible:outline-none min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto"
              onClick={handleSend}
              disabled={isLoading || !message.trim() || !wallet}
            >
              {isLoading ? (
                <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
              ) : (
                <ArrowUpIcon className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageInput;
