import { useEffect, useRef } from "react";
import { ArrowLeftIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import ChatMessage from "@/components/ChatMessage";
import MessageInput from "@/components/MessageInput";

interface ChatInterfaceProps {
  messages: any[];
  onSendMessage: (message: string) => void;
  onBackToWelcome: () => void;
  isLoading?: boolean;
}

const ChatInterface = ({
  messages,
  onSendMessage,
  onBackToWelcome,
  isLoading = false,
}: ChatInterfaceProps) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Helper function to scroll to bottom smoothly
  const scrollToBottom = (smooth: boolean = false) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages) {
      scrollToBottom();
    }
  }, [messages]);

  // Scroll to bottom when loading starts
  useEffect(() => {
    if (isLoading) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [isLoading]);

  return (
    <div className="flex flex-col h-full">
      {/* Header with back button */}
      <div className="flex items-center p-3 sm:p-4 border-b border-border/50">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToWelcome}
          className="mr-2 sm:mr-3 hover:bg-muted min-h-[44px] px-3 sm:px-4"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1 sm:mr-2" />
          <span className="text-sm sm:text-base">New Chat</span>
        </Button>
      </div>

      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto chat-container px-2 sm:px-0"
      >
        {messages.length > 0 && (
          <div className="py-4 sm:py-6">
            {messages.map((message: any) => (
              <ChatMessage key={message.id} message={message} />
            ))}
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="p-3 sm:p-4">
        <MessageInput
          onSendMessage={onSendMessage}
          isLoading={isLoading}
          compact={false}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
