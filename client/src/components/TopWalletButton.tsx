import { useState, useEffect } from "react";
import {
  ConnectButton,
  useActiveAccount,
  useActiveWallet,
  useActiveWalletChain,
} from "thirdweb/react";
import { toast } from "@/hooks/use-toast";
import { useTheme } from "@/hooks/use-theme";
import {
  ChevronDownIcon,
  WalletIcon,
  LogOutIcon,
  CopyIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  client,
  wallets,
  chains,
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
} from "@/lib/thirdweb";

// Wallet icon component using thirdweb's wallet image fetching
const CustomWalletIcon = ({
  walletId,
  size = "20",
}: {
  walletId: string;
  size?: string;
}) => {
  const [iconUrl, setIconUrl] = useState<string>("");

  useEffect(() => {
    const fetchWalletIcon = async () => {
      try {
        const { getWalletInfo } = await import("thirdweb/wallets");
        const iconUrl = await getWalletInfo(walletId as any, true);
        setIconUrl(iconUrl);
      } catch (error) {
        console.error("Failed to fetch wallet icon:", error);
        setIconUrl("");
      }
    };

    fetchWalletIcon();
  }, [walletId]);

  if (!iconUrl) {
    return (
      <div
        className="flex items-center justify-center bg-muted rounded-full text-muted-foreground text-xs"
        style={{ width: size + "px", height: size + "px" }}
      >
        👛
      </div>
    );
  }

  return (
    <img
      src={iconUrl}
      alt={`${walletId} icon`}
      className="rounded-full"
      style={{ width: size + "px", height: size + "px" }}
      onError={() => setIconUrl("")}
    />
  );
};

// Helper functions
const getWalletName = (walletId: string) => {
  switch (walletId) {
    case "io.metamask":
      return "MetaMask";
    case "com.coinbase.wallet":
      return "Coinbase Wallet";
    case "walletConnect":
      return "WalletConnect";
    default:
      return "Wallet";
  }
};

const truncateAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const getChainName = (chainId: number) => {
  switch (chainId) {
    case 1:
      return "Ethereum";
    case 137:
      return "Polygon";
    case ********:
      return "Sepolia";
    case 80002:
      return "Polygon Amoy";
    default:
      return "Unknown";
  }
};

// Enhanced connected wallet display
const ConnectedWalletDisplay = () => {
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const activeChain = useActiveWalletChain();

  if (!account?.address || !wallet) return null;

  const walletName = getWalletName(wallet.id);
  const truncatedAddress = truncateAddress(account.address);
  const chainName = activeChain ? getChainName(activeChain.id) : "Unknown";

  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(account.address);
      toast({
        title: "Address copied",
        description: "Wallet address copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy address to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleDisconnect = async () => {
    try {
      await wallet.disconnect();
      toast({
        title: "Wallet disconnected",
        description: "Your wallet has been disconnected",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to disconnect wallet",
        variant: "destructive",
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-3 px-4 py-2 h-auto bg-card/80 border-border hover:bg-primary/10 hover:border-primary/40 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 backdrop-blur-sm"
        >
          <CustomWalletIcon walletId={wallet.id} size="20" />
          <div className="flex flex-col items-start min-w-0">
            <div className="text-sm font-medium text-foreground truncate">
              {truncatedAddress}
            </div>
            <div className="text-xs text-muted-foreground">{chainName}</div>
          </div>
          <ChevronDownIcon className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-64 bg-popover border-border"
      >
        <div className="p-3">
          <div className="flex items-center gap-3 mb-2">
            <CustomWalletIcon walletId={wallet.id} size="24" />
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-popover-foreground">
                {walletName}
              </div>
              <div className="text-xs text-muted-foreground">{chainName}</div>
            </div>
          </div>
          <div className="text-xs text-muted-foreground font-mono bg-muted p-2 rounded">
            {account.address}
          </div>
        </div>
        <DropdownMenuSeparator className="bg-border" />
        <DropdownMenuItem
          onClick={copyAddress}
          className="flex items-center gap-2 text-popover-foreground hover:text-foreground hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200"
        >
          <CopyIcon className="h-4 w-4" />
          Copy Address
        </DropdownMenuItem>
        <DropdownMenuSeparator className="bg-border" />
        <DropdownMenuItem
          onClick={handleDisconnect}
          className="flex items-center gap-2 text-destructive hover:text-destructive hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200"
        >
          <LogOutIcon className="h-4 w-4" />
          Disconnect
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

/**
 * Enhanced Top Wallet Button Component
 * Positioned in the top right corner with enhanced UI
 */
const TopWalletButton = () => {
  const account = useActiveAccount();
  const { theme } = useTheme();

  const handleConnect = () => {
    // Optional: Add connection success toast
  };

  const handleDisconnect = () => {
    toast({
      title: "Wallet disconnected",
      description: "Your wallet has been disconnected",
    });
  };

  // If wallet is connected, show the enhanced connected display
  if (account?.address) {
    return <ConnectedWalletDisplay />;
  }

  // If not connected, show the connect button
  return (
    <ConnectButton
      client={client}
      wallets={wallets}
      chains={chains}
      theme={theme}
      onConnect={handleConnect}
      onDisconnect={handleDisconnect}
      connectButton={{
        label: "Sign In",
        className:
          "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-none px-6 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl",
        style: {
          minWidth: "auto !important",
          height: "auto !important",
          fontSize: "inherit !important",
        },
      }}
      connectModal={{
        title: "Sign In to Web3AI",
        size: "compact",
      }}
      detailsButton={{
        className: "hidden", // Hide default details button since we have custom one
      }}
      detailsModal={{
        assetTabs: ["token", "nft"],
      }}
      supportedTokens={{
        [ethereum.id]: [
          {
            address: "******************************************",
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygon.id]: [
          {
            address: "******************************************",
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [sepolia.id]: [
          {
            address: "******************************************",
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygonAmoy.id]: [
          {
            address: "******************************************",
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
      }}
    />
  );
};

export default TopWalletButton;
