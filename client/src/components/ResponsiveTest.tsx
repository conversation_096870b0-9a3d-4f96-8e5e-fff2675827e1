import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

const ResponsiveTest = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });
  const isMobile = useIsMobile();

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getBreakpoint = () => {
    const width = windowSize.width;
    if (width < 475) return 'xs';
    if (width < 640) return 'sm';
    if (width < 768) return 'md';
    if (width < 1024) return 'lg';
    if (width < 1280) return 'xl';
    return '2xl';
  };

  return (
    <div className="fixed bottom-4 right-4 bg-background/90 backdrop-blur-sm border border-border rounded-lg p-3 text-xs font-mono z-50 shadow-lg">
      <div className="space-y-1">
        <div>Screen: {windowSize.width} × {windowSize.height}</div>
        <div>Breakpoint: {getBreakpoint()}</div>
        <div>Mobile: {isMobile ? 'Yes' : 'No'}</div>
        <div className="flex gap-1 mt-2">
          <div className="w-2 h-2 bg-red-500 xs:bg-orange-500 sm:bg-yellow-500 md:bg-green-500 lg:bg-blue-500 xl:bg-purple-500 2xl:bg-pink-500 rounded"></div>
          <span className="text-xs">
            <span className="xs:hidden">xs</span>
            <span className="hidden xs:inline sm:hidden">xs</span>
            <span className="hidden sm:inline md:hidden">sm</span>
            <span className="hidden md:inline lg:hidden">md</span>
            <span className="hidden lg:inline xl:hidden">lg</span>
            <span className="hidden xl:inline 2xl:hidden">xl</span>
            <span className="hidden 2xl:inline">2xl</span>
          </span>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveTest;
