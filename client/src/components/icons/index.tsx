import React from 'react';

export const CubeIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m21 16-9 5-9-5V8l9-5 9 5v8z" />
      <path d="m12 3v18" />
      <path d="M3 8 12 13 21 8" />
    </svg>
  );
};

export const EthereumIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 256 417"
      fill="currentColor"
      {...props}
    >
      <path d="M127.9611 0 125.1661 9.5 125.1661 285.168 127.9611 287.958 255.9231 212.32 127.9611 0Z" />
      <path d="M127.962 0 0 212.32 127.962 287.959 127.962 154.158 127.962 0Z" />
      <path d="M127.9609 312.1866 126.3859 314.1066 126.3859 412.3056 127.9609 416.9066 255.9999 236.5866 127.9609 312.1866Z" />
      <path d="M127.962 416.9054 127.962 312.1854 0 236.5854 127.962 416.9054Z" />
      <path d="M127.9609 287.9577 255.9229 212.3207 127.9609 154.1587 127.9609 287.9577Z" />
      <path d="M0.0391 212.3208 127.9611 287.9578 127.9611 154.1588 0.0391 212.3208Z" />
    </svg>
  );
};

export const PolygonIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 38 33"
      fill="currentColor"
      {...props}
    >
      <path d="M29.3 8.5c-.9-.5-2-.5-2.9 0L20 12.2l-4.4 2.5-6.4 3.7c-.9.5-2 .5-2.9 0l-5-2.9c-.9-.5-1.5-1.5-1.5-2.6V8.2c0-1.1.6-2.1 1.5-2.6l5-2.9c.9-.5 2-.5 2.9 0l5 2.9c.9.5 1.5 1.5 1.5 2.6v7.4l4.4-2.5V5.6c0-1.1-.6-2.1-1.5-2.6l-9.4-5.4c-.9-.5-2-.5-2.9 0L1.5 3C.6 3.5 0 4.5 0 5.6v10.9c0 1.1.6 2.1 1.5 2.6l9.4 5.4c.9.5 2 .5 2.9 0l6.4-3.7 4.4-2.5 6.4-3.7c.9-.5 2-.5 2.9 0l5 2.9c.9.5 1.5 1.5 1.5 2.6v5.7c0 1.1-.6 2.1-1.5 2.6l-5 2.9c-.9.5-2 .5-2.9 0l-5-2.9c-.9-.5-1.5-1.5-1.5-2.6v-7.4l-4.4 2.5v7.4c0 1.1.6 2.1 1.5 2.6l9.4 5.4c.9.5 2 .5 2.9 0l9.4-5.4c.9-.5 1.5-1.5 1.5-2.6V16.4c0-1.1-.6-2.1-1.5-2.6l-9.4-5.3z" />
    </svg>
  );
};

export const BinanceIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 2500 2500"
      fill="currentColor"
      {...props}
    >
      <path d="M764.48,1050.52,1250,565l485.75,485.73,282.5-282.5L1250,0,482,768l282.49,282.5M0,1250,282.51,967.45,565,1249.94,282.49,1532.45Zm764.48,199.77L1250,1935l485.74-485.72,282.65,282.35-.14.15L1250,2500,482,1732l-.4-.4,282.91-282.5M1935,1250.12l282.51-282.51L2500,1250.1l-282.5,282.51Z" />
      <path d="M1536.52,1249.85h.12L1250,963.32,1038.13,1175.19l-24.34,24.35-50.2,50.21-.4.39.4.41L1250,1536.68l286.64-286.65.14-.16-.26-.02" />
    </svg>
  );
};
