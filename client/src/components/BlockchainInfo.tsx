import React, { useState, useEffect } from "react";
import {
  useActiveAccount,
  useActiveWalletChain,
  useWalletBalance,
} from "thirdweb/react";
import { getChainById } from "@/lib/chainConfig";
import { ethers } from "ethers";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowUpRight, Database, Wallet } from "lucide-react";

const BlockchainInfo = () => {
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();
  const { data: balance } = useWalletBalance({
    account: account,
    chain: activeChain,
  });

  const [gasPrice, setGasPrice] = useState<string | null>(null);
  const [blockNumber, setBlockNumber] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Extract values from v5 hooks
  const address = account?.address;
  const chainId = activeChain?.id;

  // Get chain info
  const currentChainId = chainId ? chainId.toString() : "1";
  const chain = getChainById(currentChainId);

  // Format address for display
  const formatAddress = (addr: string) => {
    if (!addr) return "";
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Format balance for display
  const formatBalance = (bal: any) => {
    if (!bal) return "0";
    return parseFloat(ethers.utils.formatEther(bal.value)).toFixed(4);
  };

  // Fetch blockchain data
  const fetchBlockchainData = async () => {
    if (!chainId || !address) return;

    setIsLoading(true);

    try {
      // Create a provider for the current chain
      // Use default RPC URLs based on chainId
      let rpcUrl = "https://eth-mainnet.g.alchemy.com/v2/********************************";

      // Set appropriate RPC URL based on chain
      if (chain?.name?.includes("Polygon")) {
        rpcUrl = "https://polygon-rpc.com";
      } else if (chain?.name?.includes("Binance")) {
        rpcUrl = "https://bsc-dataseed.binance.org";
      }

      const provider = new ethers.providers.JsonRpcProvider(rpcUrl);

      // Get current gas price
      const currentGasPrice = await provider.getGasPrice();
      setGasPrice(ethers.utils.formatUnits(currentGasPrice, "gwei"));

      // Get current block number
      const currentBlockNumber = await provider.getBlockNumber();
      setBlockNumber(currentBlockNumber);
    } catch (error) {
      console.error("Error fetching blockchain data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when address or chain changes
  useEffect(() => {
    if (address && chainId) {
      fetchBlockchainData();
    }
  }, [address, chainId]);

  // If not connected, show nothing
  if (!address) return null;

  return (
    <Card className="bg-card/50 border border-border/20 p-4 mb-6 rounded-lg backdrop-blur-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm font-medium text-foreground flex items-center">
          <Database className="h-4 w-4 mr-2 text-muted-foreground" />
          Blockchain Info
        </h3>
        <Badge
          variant="secondary"
          className="text-xs font-normal bg-muted/50 text-muted-foreground border-none"
        >
          {chain?.name || "Unknown Network"}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-3 mb-2">
        <div className="text-xs text-muted-foreground">
          <span className="block mb-1">Current Account</span>
          <span className="text-sm text-foreground font-medium">
            {formatAddress(address)}
          </span>
        </div>

        <div className="text-xs text-muted-foreground">
          <span className="block mb-1">Balance</span>
          <span className="text-sm text-foreground font-medium">
            {balance ? formatBalance(balance) : "Loading..."}{" "}
            {chain?.name === "Ethereum Mainnet" ? "ETH" : "Tokens"}
          </span>
        </div>
      </div>

      <div className="my-3"></div>

      <div className="grid grid-cols-2 gap-3">
        <div className="text-xs text-muted-foreground">
          <span className="block mb-1">Gas Price</span>
          <span className="text-sm text-foreground font-medium">
            {gasPrice
              ? `${parseFloat(gasPrice).toFixed(2)} Gwei`
              : "Loading..."}
          </span>
        </div>

        <div className="text-xs text-muted-foreground">
          <span className="block mb-1">Latest Block</span>
          <span className="text-sm text-foreground font-medium">
            {blockNumber !== null ? blockNumber.toLocaleString() : "Loading..."}
          </span>
        </div>
      </div>

      <div className="mt-3 text-xs">
        <a
          href={`https://${
            chain?.name === "Ethereum Mainnet"
              ? ""
              : `${chain?.name?.toLowerCase()}.`
          }etherscan.io/address/${address}`}
          target="_blank"
          rel="noopener noreferrer"
          className="text-muted-foreground hover:text-foreground flex items-center hover:underline transition-colors"
        >
          View on Etherscan
          <ArrowUpRight className="h-3 w-3 ml-1" />
        </a>
      </div>
    </Card>
  );
};

export default BlockchainInfo;
