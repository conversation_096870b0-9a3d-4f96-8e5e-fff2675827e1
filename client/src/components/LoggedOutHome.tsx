import TopWalletButton from "@/components/TopWalletButton";
import NonAuthMainContent from "@/components/NonAuthMainContent";
import { Button } from "@/components/ui/button";
import { CubeIcon } from "@/components/icons";
import { Sun, Moon } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";

const LoggedOutHome = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Non-signed-in users: Show full header with logo and controls */}
      <header className="flex-shrink-0 bg-background/80 backdrop-blur-sm border-b border-border/40 z-50">
        <div className="flex items-center justify-between px-6 py-4">
          {/* Left side: Web3AI Logo */}
          <div className="flex items-center gap-3">
            <div className="nebula-icon-bg w-8 h-8 flex items-center justify-center">
              <CubeIcon className="text-foreground text-lg" />
            </div>
            <h1 className="text-xl font-bold text-foreground no-underline tracking-wide">
              Web3AI
            </h1>
          </div>

          {/* Right side: Theme toggle and Sign-in button */}
          <div className="flex items-center gap-3">
            {/* Theme toggle button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleTheme}
              className="h-9 w-9 p-0 nebula-hover"
              title={
                theme === "dark"
                  ? "Switch to light mode"
                  : "Switch to dark mode"
              }
            >
              {theme === "dark" ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>

            {/* Sign-in button */}
            <TopWalletButton />
          </div>
        </div>
      </header>

      {/* Main Layout - No sidebar for non-authenticated users */}
      <div className="flex flex-col flex-1 overflow-hidden">
        <NonAuthMainContent />
      </div>
    </div>
  );
};

export default LoggedOutHome;
