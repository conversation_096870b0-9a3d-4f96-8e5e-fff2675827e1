import { ArrowRightIcon } from "lucide-react";

interface NonAuthSuggestionChipsProps {
  onSignInTrigger: (suggestion: string) => void;
}

const suggestions = [
  "Transfer tokens safely",
  "Create an ERC-20 token",
  "Buy stablecoins",
  "Audit smart contracts",
  "Check gas prices",
];

const NonAuthSuggestionChips = ({
  onSignInTrigger,
}: NonAuthSuggestionChipsProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-4 px-2">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="nebula-suggestion-chip nebula-hover text-xs sm:text-sm min-h-[36px] px-2 sm:px-3 py-1 sm:py-2"
          onClick={() => onSignInTrigger(suggestion)}
          title="Sign in to use this suggestion"
        >
          <span className="truncate">{suggestion}</span>
          <ArrowRightIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-primary ml-1 flex-shrink-0" />
        </button>
      ))}
    </div>
  );
};

export default NonAuthSuggestionChips;
