import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supportedChains, getChainById } from "@/lib/chainConfig";
import { SearchIcon, CheckIcon, ChevronLeftIcon } from "lucide-react";
import {
  useActiveWalletChain,
  useSwitchActiveWalletChain,
} from "thirdweb/react";

interface ChainSelectorProps {
  onClose?: () => void;
}

const ChainSelector = ({ onClose }: ChainSelectorProps) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Get current chain from wallet using v5 hooks
  const activeChain = useActiveWalletChain();
  const switchChain = useSwitchActiveWalletChain();

  // Get current chain ID or default to Ethereum
  const currentChainId = activeChain?.id ? activeChain.id.toString() : "1";
  const selectedChain = getChainById(currentChainId) || supportedChains[0];

  // Debug logging
  console.log("ChainSelector v5 - activeChain:", activeChain);
  console.log("ChainSelector v5 - currentChainId:", currentChainId);
  console.log("ChainSelector v5 - selectedChain:", selectedChain);

  // Filter chains based on search query
  const filteredChains = supportedChains.filter(
    (chain) =>
      chain.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chain.chainId.includes(searchQuery)
  );

  // Handle chain selection using v5 switchChain
  const handleSelectChain = async (chainId: string) => {
    const chainOption = getChainById(chainId);
    if (!chainOption) return;

    // If this is already the current chain, just close
    if (chainId === currentChainId) {
      if (onClose) onClose();
      return;
    }

    console.log("Attempting to switch to chain:", chainId, chainOption.name);

    try {
      // Create chain object for v5
      const chainToSwitch = {
        id: parseInt(chainId),
        name: chainOption.name,
        rpc: getChainRpcUrl(chainId),
        nativeCurrency: getChainNativeCurrency(chainId),
        blockExplorers: getChainBlockExplorers(chainId),
      };

      console.log("Switching to chain with v5:", chainToSwitch);

      // Use v5 switchChain function
      await switchChain(chainToSwitch);

      console.log("Successfully switched to chain:", chainId);
    } catch (error: any) {
      console.error("Failed to switch chain with v5:", error);
    }

    // Close the dropdown
    if (onClose) onClose();
  };

  // Helper functions for v5 chain data
  const getChainRpcUrl = (chainId: string): string => {
    const rpcUrls: { [key: string]: string } = {
      "1": "https://ethereum.rpc.thirdweb.com",
      "137": "https://polygon.rpc.thirdweb.com",
      "56": "https://binance.rpc.thirdweb.com",
      "11155111": "https://sepolia.rpc.thirdweb.com",
      "80002": "https://amoy.rpc.thirdweb.com",
    };
    return rpcUrls[chainId] || "https://ethereum.rpc.thirdweb.com";
  };

  const getChainNativeCurrency = (chainId: string) => {
    const currencies: { [key: string]: any } = {
      "1": { name: "Ether", symbol: "ETH", decimals: 18 },
      "137": { name: "MATIC", symbol: "MATIC", decimals: 18 },
      "56": { name: "BNB", symbol: "BNB", decimals: 18 },
      "11155111": { name: "Ether", symbol: "ETH", decimals: 18 },
      "80002": { name: "MATIC", symbol: "MATIC", decimals: 18 },
    };
    return (
      currencies[chainId] || { name: "Ether", symbol: "ETH", decimals: 18 }
    );
  };

  const getChainBlockExplorers = (chainId: string) => {
    const explorers: { [key: string]: any } = {
      "1": [{ name: "Etherscan", url: "https://etherscan.io" }],
      "137": [{ name: "Polygonscan", url: "https://polygonscan.com" }],
      "56": [{ name: "BSCScan", url: "https://bscscan.com" }],
      "11155111": [
        { name: "Sepolia Etherscan", url: "https://sepolia.etherscan.io" },
      ],
      "80002": [
        { name: "Amoy Polygonscan", url: "https://amoy.polygonscan.com" },
      ],
    };
    return (
      explorers[chainId] || [{ name: "Etherscan", url: "https://etherscan.io" }]
    );
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        if (onClose) onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div
      className="absolute bottom-full left-0 mb-2 w-64 bg-popover border border-border rounded-lg shadow-xl z-[100]"
      ref={dropdownRef}
    >
      {/* Header */}
      <div className="flex items-center gap-2 p-3 border-b border-border">
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-auto text-muted-foreground hover:text-foreground hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          onClick={onClose}
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <span className="text-popover-foreground font-medium text-sm">
          Select Network
        </span>
      </div>

      {/* Search */}
      <div className="p-3 border-b border-border">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by Name or Chain Id"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-input border-border text-foreground placeholder-muted-foreground focus:border-primary h-8 text-sm focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
      </div>

      {/* Chain List */}
      <div className="max-h-48 overflow-y-auto scrollbar-hide">
        {filteredChains.map((chainOption) => {
          // Simple comparison with v5
          const isSelected = chainOption.chainId === currentChainId;

          console.log(
            `Chain ${chainOption.name}: chainId=${chainOption.chainId}, currentChainId=${currentChainId}, isSelected=${isSelected}`
          );
          return (
            <div
              key={chainOption.id}
              className="flex items-center gap-2 p-2 cursor-pointer border-b border-border last:border-b-0 hover:bg-primary/10 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200"
              onClick={() => handleSelectChain(chainOption.id)}
            >
              {/* Checkbox */}
              <div
                className={`w-4 h-4 border-2 rounded flex items-center justify-center ${
                  isSelected
                    ? "bg-primary border-primary"
                    : "border-muted-foreground"
                }`}
              >
                {isSelected && (
                  <CheckIcon className="h-3 w-3 text-primary-foreground" />
                )}
              </div>

              {/* Chain Icon */}
              <div
                className="w-5 h-5 rounded-full flex items-center justify-center"
                style={{ backgroundColor: chainOption.color }}
              >
                <chainOption.icon className="h-3 w-3 text-white" />
              </div>

              {/* Chain Name */}
              <span className="text-popover-foreground font-medium flex-1 text-sm">
                {chainOption.name}
              </span>
            </div>
          );
        })}
      </div>

      {/* Current Chain Footer */}
      {selectedChain && (
        <div className="p-3 border-t border-border">
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: selectedChain.color }}
            />
            <span className="text-xs text-muted-foreground">
              Current: {selectedChain.name}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChainSelector;
