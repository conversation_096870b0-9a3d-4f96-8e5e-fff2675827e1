import { useState, useEffect } from "react";
import { useActiveAccount } from "thirdweb/react";
import {
  MessageCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExternalLinkIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
} from "lucide-react";
import { useChatStore } from "@/store/chatStore";
import { apiRequest } from "@/lib/queryClient";

interface ActivityItem {
  id: string;
  type: "chat" | "transaction" | "contract_interaction";
  title: string;
  description: string;
  timestamp: Date;
  status?: "pending" | "success" | "failed";
  chainId?: string;
  txHash?: string;
  chatId?: number;
}

const RecentActivity = () => {
  const { chats } = useChatStore();
  const account = useActiveAccount();
  const address = account?.address;
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Generate activity items from chats and blockchain interactions
  useEffect(() => {
    const generateActivities = async () => {
      setIsLoading(true);
      try {
        const activityItems: ActivityItem[] = [];

        // Add recent chat activities
        const recentChats = chats.slice(0, 5); // Last 5 chats
        for (const chat of recentChats) {
          // Get messages for this chat to determine activity type
          try {
            const response = await apiRequest(
              "GET",
              `/api/chats/${chat.id}/messages`
            );
            const messages = await response.json();

            if (messages.length > 0) {
              const lastMessage = messages[messages.length - 1];
              activityItems.push({
                id: `chat-${chat.id}`,
                type: "chat",
                title: chat.title,
                description: `Last message: ${lastMessage.content.substring(
                  0,
                  50
                )}...`,
                timestamp: new Date(lastMessage.timestamp),
                chatId: chat.id,
              });
            }
          } catch (error) {
            // Skip if can't fetch messages
          }
        }

        // Add mock blockchain activities (in a real app, these would come from blockchain APIs)
        if (address) {
          const mockTransactions: ActivityItem[] = [
            {
              id: "tx-1",
              type: "transaction",
              title: "ETH Transfer",
              description: "Sent 0.1 ETH to 0x742d...35Cc",
              timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
              status: "success",
              chainId: "1",
              txHash: "0x1234567890abcdef...",
            },
            {
              id: "tx-2",
              type: "contract_interaction",
              title: "Token Swap",
              description: "Swapped 100 USDC for ETH on Uniswap",
              timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
              status: "success",
              chainId: "1",
              txHash: "0xabcdef1234567890...",
            },
            {
              id: "tx-3",
              type: "contract_interaction",
              title: "NFT Mint",
              description: 'Minted NFT from collection "CryptoPunks"',
              timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
              status: "pending",
              chainId: "1",
              txHash: "0xfedcba0987654321...",
            },
          ];
          activityItems.push(...mockTransactions);
        }

        // Sort by timestamp (most recent first)
        activityItems.sort(
          (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
        );

        setActivities(activityItems.slice(0, 10)); // Show last 10 activities
      } catch (error) {
        console.error("Error generating activities:", error);
      } finally {
        setIsLoading(false);
      }
    };

    generateActivities();
  }, [chats, address]);

  const getActivityIcon = (activity: ActivityItem) => {
    switch (activity.type) {
      case "chat":
        return <MessageCircleIcon className="h-4 w-4" />;
      case "transaction":
        return activity.description.toLowerCase().includes("sent") ||
          activity.description.toLowerCase().includes("transfer") ? (
          <ArrowUpIcon className="h-4 w-4" />
        ) : (
          <ArrowDownIcon className="h-4 w-4" />
        );
      case "contract_interaction":
        return <ExternalLinkIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "success":
        return <CheckCircleIcon className="h-3 w-3 text-green-500" />;
      case "failed":
        return <XCircleIcon className="h-3 w-3 text-red-500" />;
      case "pending":
        return <AlertCircleIcon className="h-3 w-3 text-yellow-500" />;
      default:
        return null;
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-muted rounded-md"></div>
              <div className="flex-1 space-y-2">
                <div className="h-3 bg-muted rounded w-3/4"></div>
                <div className="h-2 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <ClockIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No recent activity</p>
        <p className="text-xs text-muted-foreground mt-1">
          Start chatting or connect your wallet to see activity
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {activities.map((activity) => (
        <div
          key={activity.id}
          className="flex items-start gap-3 p-3 rounded-md hover:bg-background/80 transition-colors cursor-pointer"
          onClick={() => {
            if (activity.type === "chat" && activity.chatId) {
              // Handle chat selection - this would need to be passed as a prop
              console.log("Select chat:", activity.chatId);
            } else if (activity.txHash) {
              // Open transaction in explorer
              const explorerUrl =
                activity.chainId === "1"
                  ? `https://etherscan.io/tx/${activity.txHash}`
                  : `https://polygonscan.com/tx/${activity.txHash}`;
              window.open(explorerUrl, "_blank");
            }
          }}
        >
          <div className="flex-shrink-0 w-10 h-10 bg-muted/50 rounded-md flex items-center justify-center text-muted-foreground">
            {getActivityIcon(activity)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-2">
              <p className="text-sm font-medium text-foreground truncate">
                {activity.title}
              </p>
              <div className="flex items-center gap-1">
                {getStatusIcon(activity.status)}
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatTimeAgo(activity.timestamp)}
                </span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground truncate mt-1">
              {activity.description}
            </p>
            {activity.chainId && (
              <div className="flex items-center gap-1 mt-1">
                <span className="text-xs bg-muted/50 px-2 py-0.5 rounded text-muted-foreground">
                  Chain {activity.chainId}
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default RecentActivity;
